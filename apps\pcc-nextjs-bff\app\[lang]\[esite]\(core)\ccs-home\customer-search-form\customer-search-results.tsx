'use client';

import { BlocksPageChangeEvent } from '@blocks-web-components/events';
import {
  CatCard,
  CatDropdown,
  CatList,
  CatListItem,
  CatPagination,
  CatTable,
  CatTableObject,
  SelectValue
} from 'components/blocks-components';
import { useTranslations } from 'next-intl';
import { usePathname, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CustomerSearchTable } from './customer-search-table';
import CustomerSearchTableHeader from './customer-search-table-header';
import { SORT_DIRECTIONS, SORT_TYPE } from './types';
import styles from './customer-search-table.module.scss';
import CsrAdpModal from './csr-adp-modal';

// Constants
const PAGE_PARAM = 'page';
const PAGE_SIZE_PARAM = 'pageSize';
const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 12;
const PAGE_SIZE_OPTIONS = [12, 24, 48, 96];

export const CustomerSearchResults = ({
  searchResults,
  totalCount,
  currentPage,
  pageSize,
  setCurrentPage,
  setPageSize,
  onSort,
  onPaginationChange
}) => {
  const t = useTranslations();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const tableContainer = useRef<HTMLDivElement | null>(null);
  const [optimisticPage, setOptimisticPage] = useState(currentPage);
  const [optimisticPageSize, setOptimisticPageSize] = useState(pageSize);

  // CSR ADP Modal state
  const [isCsrAdpModalOpen, setIsCsrAdpModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<{
    userId: string;
    logonId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null>(null);

  const totalRecords = useMemo(
    () => totalCount || searchResults.length,
    [totalCount, searchResults]
  );

  const paginatedCustomers = useMemo(() => {
    return searchResults || [];
  }, [searchResults]);

  // CSR ADP Modal handlers
  const handleOpenCsrAdpModal = useCallback(
    (customer: {
      userId: string;
      logonId: string;
      firstName?: string;
      lastName?: string;
      email?: string;
    }) => {
      setSelectedCustomer(customer);
      setIsCsrAdpModalOpen(true);
    },
    []
  );

  const handleCloseCsrAdpModal = useCallback(() => {
    setIsCsrAdpModalOpen(false);
    setSelectedCustomer(null);
  }, []);

  const updatePaginationParams = useCallback(
    (page: number, size: number) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(PAGE_PARAM, page.toString());
      params.set(PAGE_SIZE_PARAM, size.toString());
      window.history.pushState(null, '', `${pathname}?${params.toString()}`);
    },
    [pathname, searchParams]
  );

  const handlePageChange = useCallback(
    (e: BlocksPageChangeEvent) => {
      const nextPage = e?.detail?.pageNumber;
      const nextPageSize = e?.detail?.pageSize || optimisticPageSize;
      setOptimisticPage(nextPage);
      if (nextPageSize !== optimisticPageSize) {
        setOptimisticPageSize(nextPageSize);
      }
      updatePaginationParams(nextPage, nextPageSize);

      // Update API call for pagination
      setCurrentPage(nextPage);
      if (nextPageSize !== optimisticPageSize) {
        setPageSize(nextPageSize);
      }
      onPaginationChange(nextPage, nextPageSize);
    },
    [
      updatePaginationParams,
      optimisticPageSize,
      setCurrentPage,
      setPageSize,
      onPaginationChange
    ]
  );

  const handlePageSizeChange = useCallback(
    (size: number) => {
      setOptimisticPageSize(size);
      setOptimisticPage(1);
      updatePaginationParams(1, size);

      // Update parent state and trigger API call for pagination
      setPageSize(size);
      setCurrentPage(1);
      onPaginationChange(1, size);
    },
    [updatePaginationParams, setPageSize, setCurrentPage, onPaginationChange]
  );

  useEffect(() => {
    if (searchParams) {
      setOptimisticPage(Number(searchParams.get(PAGE_PARAM) || DEFAULT_PAGE));
      setOptimisticPageSize(
        Number(searchParams.get(PAGE_SIZE_PARAM) || DEFAULT_PAGE_SIZE)
      );
    }
  }, [searchParams]);

  const handleTableSort = useCallback(
    (
      newSortBy: string,
      newSortType: SORT_TYPE,
      newSortDirection: SORT_DIRECTIONS
    ) => {
      if (onSort) {
        onSort(newSortBy, newSortType, newSortDirection);
      }
    },
    [onSort]
  );

  // Return null if there are no search results
  if (!searchResults) {
    return null;
  }

  return (
    <div className="mt-4 mb-4">
      <CatCard className="w-100 h-100 overflow-hidden">
        {paginatedCustomers.length > 0 ? (
          <div ref={tableContainer}>
            <div className="d-flex justify-content-end align-items-center p-3">
              <div className="d-flex align-items-center">
                <span className="me-2 cat-u-theme-typography-body-sm me-2 text-neutral-90 fw-bold">
                  {t('STORE_LIST_SHOW')}
                </span>
                <CatDropdown
                  size="sm"
                  className="me-2 w-px"
                  value={optimisticPageSize.toString() as SelectValue}
                  onBlSelect={(e) => {
                    handlePageSizeChange(Number(e.detail.value));
                  }}
                  hideLabel
                  placeholder={optimisticPageSize.toString()}
                >
                  <CatList>
                    {PAGE_SIZE_OPTIONS.map((size) => (
                      <CatListItem
                        key={size}
                        value={size.toString() as SelectValue}
                      >
                        {size}
                      </CatListItem>
                    ))}
                  </CatList>
                </CatDropdown>
              </div>
            </div>
            <CatTableObject height="auto">
              <CatTable>
                <CustomerSearchTableHeader
                  onSort={handleTableSort}
                  currentPage={optimisticPage}
                />
                <CustomerSearchTable
                  paginatedCustomers={paginatedCustomers}
                  onCustomerClick={handleOpenCsrAdpModal}
                />
              </CatTable>
              <CatPagination
                pageSizeLabel={t('STORE_LIST_SHOW')}
                data-testid="pagination-bottom-tool-bar"
                arialabel="Pagination footer"
                slot="footer"
                totalRecords={totalRecords}
                pageSize={optimisticPageSize}
                currentItem={optimisticPage}
                onBlPageChange={handlePageChange}
                pageSizeOptions={PAGE_SIZE_OPTIONS}
                className={styles.catPagination}
              />
            </CatTableObject>
          </div>
        ) : (
          <div style={{ height: '312px' }}>
            <div className="d-flex justify-content-center align-items-center p-4 pb-3 cat-u-theme-typography-body-sm h-100">
              {t('NO_RESULTS_FOUND_PLEASE_MODIFY')}
            </div>
          </div>
        )}
      </CatCard>

      {/* CSR ADP Modal */}
      <CsrAdpModal
        isOpen={isCsrAdpModalOpen}
        onClose={handleCloseCsrAdpModal}
        selectedCustomer={selectedCustomer}
      />
    </div>
  );
};
