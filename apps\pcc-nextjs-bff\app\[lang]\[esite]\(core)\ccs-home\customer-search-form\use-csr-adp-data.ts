import { useState, useEffect } from 'react';
import { DealerResponse } from 'services/dealer';
import { DealerAssociationResponse, DealerErrorResponse } from 'services/dealer-association';
import { LocationAddress } from 'services/location/types';

type CsrAdpData = {
  isLoggedIn: boolean;
  isDealerUser: boolean;
  isInstantAccessUser: boolean;
  isCspUser: boolean;
  dealerUserResponse: DealerResponse;
  registeredUserResponse: DealerAssociationResponse | DealerErrorResponse;
  storeName: string;
  storeAddress: LocationAddress;
  showSkipThisStep: boolean;
  restrictedDealerStores: string[];
};

type UseCsrAdpDataResult = {
  data: CsrAdpData | null;
  isLoading: boolean;
  error: string | null;
};

export const useCsrAdpData = (shouldFetch: boolean): UseCsrAdpDataResult => {
  const [data, setData] = useState<CsrAdpData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!shouldFetch) {
      return;
    }

    const fetchAdpData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Create an API endpoint to fetch the ADP data
        const response = await fetch('/api/csr/adp-data', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch ADP data: ${response.statusText}`);
        }

        const adpData = await response.json();
        setData(adpData);
      } catch (err) {
        console.error('Error fetching CSR ADP data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAdpData();
  }, [shouldFetch]);

  return { data, isLoading, error };
};
