'use client';

import React from 'react';
import {
  CatModal,
  CatIconClose,
  CatButton
} from 'components/blocks-components';
import { useTranslations } from 'next-intl';

type CsrAdpModalProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomer: {
    userId: string;
    logonId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null;
};

export const CsrAdpModal = ({
  isOpen,
  onClose,
  selectedCustomer
}: CsrAdpModalProps) => {
  const t = useTranslations();

  if (!isOpen || !selectedCustomer) {
    return null;
  }

  return (
    <CatModal
      isActive={isOpen}
      onBlModalClose={onClose}
      size="lg"
      heading={t('CSR_ADP_MODAL_TITLE')}
      headingVariant="title"
      dismissible
    >
      <div className="position-relative">
        {/* Close icon */}
        <button
          type="button"
          className="btn-close position-absolute top-0 end-0 p-2"
          aria-label={t('CSR_ADP_MODAL_CLOSE')}
          onClick={onClose}
          style={{ zIndex: 1 }}
        >
          <CatIconClose />
        </button>

        {/* Modal content */}
        <div className="pt-3">
          <div className="mb-4">
            <p className="mb-2">
              <strong>{t('CSR_ADP_MODAL_CUSTOMER_LABEL')}:</strong>{' '}
              {selectedCustomer.firstName && selectedCustomer.lastName
                ? `${selectedCustomer.firstName} ${selectedCustomer.lastName}`
                : selectedCustomer.logonId}
            </p>
            {selectedCustomer.email && (
              <p className="mb-2 text-muted">
                <strong>{t('CSR_ADP_MODAL_EMAIL_LABEL')}:</strong>{' '}
                {selectedCustomer.email}
              </p>
            )}
          </div>

          {/* CSR ADP Content */}
          <div className="mb-4">
            <h5 className="mb-3">Associated Dealer Program</h5>
            <p className="mb-3">
              This feature allows CSR users to manage dealer associations for
              the selected customer.
            </p>
            <div className="alert alert-info">
              <strong>Note:</strong> CSR ADP functionality is currently being
              implemented. This modal will be enhanced with full dealer
              association capabilities.
            </div>
          </div>

          {/* Action buttons */}
          <div className="d-flex justify-content-end gap-2">
            <CatButton variant="secondary" onClick={onClose}>
              Cancel
            </CatButton>
            <CatButton variant="primary" onClick={onClose}>
              Continue
            </CatButton>
          </div>
        </div>
      </div>
    </CatModal>
  );
};

export default CsrAdpModal;
