'use client';

import React from 'react';
import {
  CatModal,
  CatIconClose,
  CatButton
} from 'components/blocks-components';
import { useTranslations } from 'next-intl';
import AssociatedDealerContainer from 'components/associated-dealers/associated-dealer-container';
import { TranslationsProvider } from 'providers/translations-provider';
import { FeedbackProvider } from 'providers/feedback-provider';
import { DealerLocatorDrawerProvider } from 'components/dealer-locator/dealer-locator-drawer-context';
import { AssociatedDealerProvider } from 'components/associated-dealers/associated-dealer-context';
import { adpKeys } from 'components/associated-dealers/constants/translation-keys';

import { useCsrAdpData } from './use-csr-adp-data';

type CsrAdpModalProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomer: {
    userId: string;
    logonId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null;
};

export const CsrAdpModal = ({
  isOpen,
  onClose,
  selectedCustomer
}: CsrAdpModalProps) => {
  const t = useTranslations();

  // Fetch ADP data when modal is open
  const {
    data: adpData,
    isLoading,
    error
  } = useCsrAdpData(isOpen && !!selectedCustomer);

  if (!isOpen || !selectedCustomer) {
    return null;
  }

  return (
    <CatModal
      isActive={isOpen}
      onBlModalClose={onClose}
      size="lg"
      heading={t('CSR_ADP_MODAL_TITLE')}
      headingVariant="title"
      dismissible
    >
      <div className="position-relative">
        {/* Close icon */}
        <button
          type="button"
          className="btn-close position-absolute top-0 end-0 p-2"
          aria-label={t('CSR_ADP_MODAL_CLOSE')}
          onClick={onClose}
          style={{ zIndex: 1 }}
        >
          <CatIconClose />
        </button>

        {/* Modal content */}
        <div className="pt-3">
          <div className="mb-4">
            <p className="mb-2">
              <strong>{t('CSR_ADP_MODAL_CUSTOMER_LABEL')}:</strong>{' '}
              {selectedCustomer.firstName && selectedCustomer.lastName
                ? `${selectedCustomer.firstName} ${selectedCustomer.lastName}`
                : selectedCustomer.logonId}
            </p>
            {selectedCustomer.email && (
              <p className="mb-2 text-muted">
                <strong>{t('CSR_ADP_MODAL_EMAIL_LABEL')}:</strong>{' '}
                {selectedCustomer.email}
              </p>
            )}
          </div>

          {/* CSR ADP Content */}
          {isLoading ? (
            // Loading state
            <div className="mb-4 text-center">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2">Loading ADP data...</p>
            </div>
          ) : error ? (
            // Error state
            <div className="mb-4">
              <div className="alert alert-warning">
                <strong>Warning:</strong> Unable to load ADP data. {error}
              </div>
              <div className="d-flex justify-content-end gap-2">
                <CatButton variant="outline" onClick={onClose}>
                  Close
                </CatButton>
              </div>
            </div>
          ) : adpData ? (
            // Full ADP functionality with real data
            <TranslationsProvider keys={adpKeys}>
              <FeedbackProvider>
                <AssociatedDealerProvider>
                  <DealerLocatorDrawerProvider>
                    <AssociatedDealerContainer
                      isLoggedIn={adpData.isLoggedIn}
                      isDealerUser={adpData.isDealerUser}
                      isInstantAccessUser={adpData.isInstantAccessUser}
                      isCspUser={adpData.isCspUser}
                      dealerUserResponse={adpData.dealerUserResponse}
                      registeredUserResponse={adpData.registeredUserResponse}
                      storeName={adpData.storeName}
                      storeAddress={adpData.storeAddress}
                      openAdpModal={true}
                      showSkipThisStep={adpData.showSkipThisStep}
                      restrictedDealerStores={adpData.restrictedDealerStores}
                    />
                  </DealerLocatorDrawerProvider>
                </AssociatedDealerProvider>
              </FeedbackProvider>
            </TranslationsProvider>
          ) : (
            // Simplified version when data is not available
            <>
              <div className="mb-4">
                <h5 className="mb-3">Associated Dealer Program</h5>
                <p className="mb-3">
                  This feature allows CSR users to manage dealer associations
                  for the selected customer.
                </p>
                <div className="alert alert-info">
                  <strong>Note:</strong> CSR ADP functionality is currently
                  being implemented. This modal will be enhanced with full
                  dealer association capabilities.
                </div>
              </div>

              {/* Action buttons */}
              <div className="d-flex justify-content-end gap-2">
                <CatButton variant="outline" onClick={onClose}>
                  Cancel
                </CatButton>
                <CatButton variant="primary" onClick={onClose}>
                  Continue
                </CatButton>
              </div>
            </>
          )}
        </div>
      </div>
    </CatModal>
  );
};

export default CsrAdpModal;
