'use client';

import React from 'react';
import { CatModal, CatIconClose } from 'components/blocks-components';
import { useTranslations } from 'next-intl';
import AssociatedDealerModalContent from 'components/associated-dealers/components/associated-dealer-modal/associated-dealer-modal-content';
import { LocationAddress } from 'services/location/types';
import { DealerResponse } from 'services/dealer';
import {
  DealerAssociationResponse,
  DealerErrorResponse
} from 'services/dealer-association';

type CsrAdpModalProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomer: {
    userId: string;
    logonId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null;
  // Props needed for AssociatedDealerModalContent
  isLoggedIn: boolean;
  isDealerUser: boolean;
  isInstantAccessUser: boolean;
  isCspUser: boolean;
  dealerUserResponse: DealerResponse;
  registeredUserResponse: DealerAssociationResponse | DealerErrorResponse;
  storeName: string;
  storeAddress: LocationAddress;
  showSkipThisStep: boolean;
  isStoreLocationTandCFlag?: boolean;
  entitlementResponse?: {
    redirectURL: string;
    isMicoCookieRequired: boolean;
  };
  isStoreLocationListViewUpdateFlag?: boolean;
  isAssociatedDealerModalFlag?: boolean;
  isStoreLocationMapViewUpdateFlag?: boolean;
  isPCCAssociatedDealerFFEnabled?: boolean;
  isStoreLocationOptimizationFlag?: boolean;
  restrictedDealerStores?: string[];
};

export const CsrAdpModal = ({
  isOpen,
  onClose,
  selectedCustomer,
  isLoggedIn,
  isDealerUser,
  isInstantAccessUser,
  isCspUser,
  dealerUserResponse,
  registeredUserResponse,
  storeName,
  storeAddress,
  showSkipThisStep,
  isStoreLocationTandCFlag,
  entitlementResponse,
  isStoreLocationListViewUpdateFlag,
  isAssociatedDealerModalFlag,
  isStoreLocationMapViewUpdateFlag,
  isPCCAssociatedDealerFFEnabled,
  isStoreLocationOptimizationFlag,
  restrictedDealerStores
}: CsrAdpModalProps) => {
  const t = useTranslations();

  if (!isOpen || !selectedCustomer) {
    return null;
  }

  return (
    <CatModal
      isActive={isOpen}
      onBlModalClose={onClose}
      size="lg"
      heading={t('CSR_ADP_MODAL_TITLE')}
      headingVariant="title"
      dismissible
    >
      <div className="position-relative">
        {/* Close icon */}
        <button
          type="button"
          className="btn-close position-absolute top-0 end-0 p-2"
          aria-label={t('CSR_ADP_MODAL_CLOSE')}
          onClick={onClose}
          style={{ zIndex: 1 }}
        >
          <CatIconClose />
        </button>

        {/* Modal content */}
        <div className="pt-3">
          <div className="mb-3">
            <p className="mb-1">
              <strong>{t('CSR_ADP_MODAL_CUSTOMER_LABEL')}:</strong>{' '}
              {selectedCustomer.firstName && selectedCustomer.lastName
                ? `${selectedCustomer.firstName} ${selectedCustomer.lastName}`
                : selectedCustomer.logonId}
            </p>
            {selectedCustomer.email && (
              <p className="mb-1 text-muted">
                <strong>{t('CSR_ADP_MODAL_EMAIL_LABEL')}:</strong>{' '}
                {selectedCustomer.email}
              </p>
            )}
          </div>

          <AssociatedDealerModalContent
            isLoggedIn={isLoggedIn}
            isDealerUser={isDealerUser}
            isInstantAccessUser={isInstantAccessUser}
            isCspUser={isCspUser}
            dealerUserResponse={dealerUserResponse}
            registeredUserResponse={registeredUserResponse}
            storeName={storeName}
            storeAddress={storeAddress}
            openAdpModal={true}
            showSkipThisStep={showSkipThisStep}
            isStoreLocationTandCFlag={isStoreLocationTandCFlag}
            entitlementResponse={entitlementResponse}
            isStoreLocationListViewUpdateFlag={
              isStoreLocationListViewUpdateFlag
            }
            isAssociatedDealerModalFlag={isAssociatedDealerModalFlag}
            isStoreLocationMapViewUpdateFlag={isStoreLocationMapViewUpdateFlag}
            isPCCAssociatedDealerFFEnabled={isPCCAssociatedDealerFFEnabled}
            isStoreLocationOptimizationFlag={isStoreLocationOptimizationFlag}
            restrictedDealerStores={restrictedDealerStores}
          />
        </div>
      </div>
    </CatModal>
  );
};

export default CsrAdpModal;
