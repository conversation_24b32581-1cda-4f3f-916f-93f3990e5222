import '@testing-library/jest-dom';
import { render, screen, fireEvent } from '@testing-library/react';
import { CustomerSearchTable } from './customer-search-table';

const mockCustomers = [
  {
    id: '1',
    logonId: 'user1',
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Company A',
    address: {
      line1: '123 Main St',
      country: 'US',
      city: 'NewYork',
      state: 'NY',
      email: '<EMAIL>',
      phone: '1234567890'
    },
    lastActiveDateTime: '2024-01-15'
  }
];

describe('CustomerSearchTable', () => {
  it('should render customer search table with customer data', () => {
    render(<CustomerSearchTable paginatedCustomers={mockCustomers} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('1234567890')).toBeInTheDocument();
    expect(screen.getByText('Company A')).toBeInTheDocument();
  });

  it('should render logonId as clickable link', () => {
    render(<CustomerSearchTable paginatedCustomers={mockCustomers} />);
    const logonIdLink = screen.getByText('user1');
    expect(logonIdLink).toBeInTheDocument();
    expect(logonIdLink.closest('cat-text-link')).toBeInTheDocument();
  });

  it('should call onCustomerClick when logonId is clicked', () => {
    const mockOnCustomerClick = jest.fn();
    render(
      <CustomerSearchTable
        paginatedCustomers={mockCustomers}
        onCustomerClick={mockOnCustomerClick}
      />
    );

    const logonIdLink = screen.getByText('user1');
    fireEvent.click(logonIdLink);

    expect(mockOnCustomerClick).toHaveBeenCalledWith({
      userId: '1',
      logonId: 'user1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>'
    });
  });

  it('should not call onCustomerClick when onCustomerClick prop is not provided', () => {
    const consoleSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {});
    render(<CustomerSearchTable paginatedCustomers={mockCustomers} />);

    const logonIdLink = screen.getByText('user1');
    fireEvent.click(logonIdLink);

    // Should not throw any errors
    expect(consoleSpy).not.toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
});
