'use client';
import { Conditional } from '@cat-ecom/pcc-components';
import {
  CatTableBody,
  CatTableCell,
  CatTableRow,
  CatTextLink
} from 'components/blocks-components';
import styles from './customer-search-table.module.scss';

export const CustomerSearchTable = ({
  paginatedCustomers,
  onCustomerClick
}: {
  paginatedCustomers: any[];
  onCustomerClick?: (customer: {
    userId: string;
    logonId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  }) => void;
}) => {
  const formatName = (firstName, lastName) => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (firstName) {
      return firstName;
    } else if (lastName) {
      return lastName;
    }
    return '-';
  };

  const formattedDate = (isoString: string) => {
    return new Date(isoString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit'
    });
  };

  const displayProfileAddress = (city: string, state: string) => {
    if (city && state) {
      return `${city},${state}`;
    } else if (city) {
      return city;
    } else if (state) {
      return state;
    }
    return '-';
  };

  const handleLogonIdClick = (customer: any) => {
    if (onCustomerClick) {
      onCustomerClick({
        userId: customer.id || customer.userId || customer.logonId, // Use available ID field
        logonId: customer.logonId,
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.address?.email || customer.email
      });
    }
  };

  return (
    <CatTableBody className="w-100">
      <Conditional test={paginatedCustomers.length > 0}>
        {paginatedCustomers.map((customer) => {
          return (
            <CatTableRow
              key={customer.id || customer.email || customer.logonId}
            >
              <CatTableCell width="auto" isFrozen className={styles.wrapText}>
                <CatTextLink
                  onClick={() => handleLogonIdClick(customer)}
                  style={{ cursor: 'pointer' }}
                >
                  {customer?.logonId ?? '-'}
                </CatTextLink>
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {formatName(customer?.firstName, customer?.lastName)}
              </CatTableCell>
              <CatTableCell
                width="auto"
                className={styles.wrapText}
                align="center"
              >
                {customer?.address?.phone ?? '-'}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {customer?.address?.email ?? '-'}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {customer?.companyName ?? '-'}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {displayProfileAddress(
                  customer?.address?.city,
                  customer?.address?.state
                )}
              </CatTableCell>
              <CatTableCell width="auto" className={styles.wrapText}>
                {customer?.lastActiveDateTime
                  ? formattedDate(customer?.lastActiveDateTime)
                  : '-'}
              </CatTableCell>
            </CatTableRow>
          );
        })}
      </Conditional>
    </CatTableBody>
  );
};
