import { NextRequest, NextResponse } from 'next/server';
import { getUser } from 'services/user';
import { UserCustomerTypes } from 'services/user/types';
import { getUserStoreLocation } from 'services/location';
import { getADPDealerPreferencesInfo } from 'services/dealer-association/actions';
import { getDealerAssociation } from 'services/dealer-association';
import { getDealer } from 'services/dealer';
import { NO_DEALER_ASSOCIATION_ERROR } from 'components/associated-dealers/components/associated-dealer-drawer/constants';
import { DealerAssociationResponse } from 'services/dealer-association';
import { DealerResponse } from 'services/dealer';

export async function GET(request: NextRequest) {
  try {
    // Get user information
    const {
      customerType,
      userAccessFlags: { isInstantAccessUser, isLoggedIn, isCspUser },
      restrictedDealerStores = []
    } = await getUser();

    // Get store location
    const { name, address } = await getUserStoreLocation();
    const isDealerUser = UserCustomerTypes.Dealer === customerType;

    let dealerAssociationResponse: DealerAssociationResponse;
    let dealerUserResponse: DealerResponse;
    let showSkipThisStep = false;

    // Get dealer data based on user type
    if (!isDealerUser) {
      try {
        dealerAssociationResponse = await getDealerAssociation();
      } catch (error) {
        const parsedError = JSON.parse(error.message);
        if (parsedError.errors[0].errorKey === NO_DEALER_ASSOCIATION_ERROR) {
          dealerAssociationResponse = { associations: [] };
        } else {
          throw error;
        }
      }
    } else {
      dealerUserResponse = await getDealer();
    }

    // Get ADP preferences info
    try {
      const adpInfo = await getADPDealerPreferencesInfo(isDealerUser);
      if (adpInfo.dealerAssociationResponse) {
        dealerAssociationResponse = adpInfo.dealerAssociationResponse;
      }
      if (adpInfo.dealerUserResponse) {
        dealerUserResponse = adpInfo.dealerUserResponse;
      }
      showSkipThisStep = adpInfo.isStoreAvailable || false;
    } catch (error) {
      console.warn('Failed to get ADP preferences info:', error);
      // Continue with existing data
    }

    const responseData = {
      isLoggedIn,
      isDealerUser,
      isInstantAccessUser,
      isCspUser,
      dealerUserResponse,
      registeredUserResponse: dealerAssociationResponse,
      storeName: name,
      storeAddress: address,
      showSkipThisStep,
      restrictedDealerStores
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error fetching CSR ADP data:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch ADP data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
